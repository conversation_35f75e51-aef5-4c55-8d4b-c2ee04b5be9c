# 🎮 VALORANT MATCHMAKER
## Tournament Bracket Generator

A sleek, Valorant-themed local tournament bracket generator tool with a Python FastAPI backend and modern HTML/CSS/JS frontend.

![Valorant Theme](https://img.shields.io/badge/Theme-VALORANT-FF4655?style=for-the-badge)
![Python](https://img.shields.io/badge/Backend-FastAPI-009688?style=for-the-badge)
![Frontend](https://img.shields.io/badge/Frontend-HTML%2FCSS%2FJS-FF6B35?style=for-the-badge)

## ✨ Features

- **🎨 Valorant-Themed UI**: Dark background with neon red/cyan accents and futuristic fonts
- **👥 Team Management**: Add teams with 5 players each, including rank selection
- **🏆 Rank-Based Seeding**: Automatic seeding based on average team rank scores
- **🌳 Tree-Style Brackets**: Beautiful single elimination bracket visualization
- **💾 Local Storage**: All data stored in local JSON files (no database required)
- **📱 Responsive Design**: Works on desktop and mobile devices

## 🚀 Quick Start

### Prerequisites
- Python 3.8+
- Modern web browser

### Installation & Setup

1. **Install Python Dependencies**
   ```bash
   cd backend
   pip install -r requirements.txt
   ```

2. **Start the Backend Server**
   ```bash
   cd backend
   python app.py
   ```
   The API will be available at `http://localhost:8000`

3. **Open the Frontend**
   - Open `frontend/index.html` in your web browser
   - Or serve it via a local web server:
     ```bash
     cd frontend
     python -m http.server 3000
     ```
     Then visit `http://localhost:3000`

## 📁 Project Structure

```
valorant-matchmaker/
├── backend/
│   ├── app.py                # FastAPI server
│   ├── logic/
│   │   ├── bracket_gen.py    # Bracket generation algorithm
│   │   └── rank_map.py       # Rank score mappings
│   ├── data/
│   │   ├── teams.json        # Local team storage
│   │   └── brackets.json     # Local bracket storage
│   └── requirements.txt
│
├── frontend/
│   ├── index.html            # Main team entry page
│   ├── bracket.html          # Full bracket visualization
│   ├── style.css             # Valorant-themed styling
│   ├── app.js                # Frontend logic
│   └── assets/
│       └── ranks/            # Valorant rank PNGs
│
└── README.md
```

## 🎯 How to Use

### 1. Add Teams
- Enter team name and optional icon URL
- Add 5 players with their names and Valorant ranks
- Click "Add Team" to save

### 2. Generate Bracket
- Add at least 2 teams
- Click "Generate Tournament Bracket"
- View the bracket in the Bracket tab

### 3. View Full Bracket
- Click "View Full Bracket" for a tree-style visualization
- See seeded matchups and tournament progression

## 🏅 Rank Scoring System

Teams are automatically seeded based on average player ranks:

| Rank | Score |
|------|-------|
| Iron | 1 |
| Bronze | 2 |
| Silver | 3 |
| Gold | 4 |
| Platinum | 5 |
| Diamond | 6 |
| Ascendant | 7 |
| Immortal | 8 |
| Radiant | 9 |

## 🔧 API Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/teams` | Get all teams |
| POST | `/teams/add` | Add a new team |
| DELETE | `/teams/{name}` | Delete a team |
| POST | `/bracket/generate` | Generate tournament bracket |
| GET | `/bracket` | Get current bracket |
| DELETE | `/bracket` | Clear bracket |

## 🎨 Design Features

- **Dark Theme**: `#0F1923` background with `#1E2328` cards
- **Neon Accents**: Red (`#FF4655`) and cyan (`#00FFFF`) highlights
- **Modern Fonts**: Rajdhani and Orbitron for that futuristic feel
- **Smooth Animations**: Hover effects and transitions
- **Responsive Layout**: CSS Grid and Flexbox for all screen sizes

## 🔮 Future Enhancements

- [ ] Sound effects on interactions
- [ ] Export bracket as image
- [ ] Double elimination brackets
- [ ] Match result tracking
- [ ] Tournament history
- [ ] Team statistics

## 🛠️ Development

### Backend Development
The FastAPI backend provides RESTful endpoints for team and bracket management. All data is stored in JSON files for simplicity.

### Frontend Development
Pure HTML/CSS/JS frontend with modern ES6+ features. No frameworks required - just open in a browser!

### Styling
The CSS uses CSS custom properties (variables) for easy theme customization. The Valorant color scheme is defined in `:root`.

## 📝 License

This project is open source and available under the MIT License.

## 🎮 Enjoy Your Tournament!

Create epic Valorant tournaments with style! The tool handles all the seeding and bracket generation automatically based on player ranks.

---

*Built with ❤️ for the Valorant community*
