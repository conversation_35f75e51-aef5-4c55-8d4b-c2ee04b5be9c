"""
Tournament bracket generation logic
"""
import math
import random
from typing import List, Dict, Any

def generate_single_elimination_bracket(teams: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    Generate a single elimination bracket with proper seeding
    """
    if not teams:
        return {"rounds": []}
    
    # Sort teams by seed score (highest first)
    sorted_teams = sorted(teams, key=lambda x: x.get('seedScore', 0), reverse=True)
    
    # Calculate number of rounds needed
    num_teams = len(sorted_teams)
    num_rounds = math.ceil(math.log2(num_teams)) if num_teams > 1 else 1
    bracket_size = 2 ** num_rounds
    
    # Create first round with seeded matchups
    first_round_matches = []
    
    if num_teams == 1:
        # Special case: only one team
        return {
            "rounds": [{
                "round": 1,
                "matches": [{
                    "team1": sorted_teams[0]["name"],
                    "team2": "BYE",
                    "winner": sorted_teams[0]["name"]
                }]
            }]
        }
    
    # Add BYEs if needed
    teams_with_byes = sorted_teams.copy()
    byes_needed = bracket_size - num_teams
    
    for i in range(byes_needed):
        teams_with_byes.append({"name": "BYE", "seedScore": -1})
    
    # Create seeded matchups (1 vs last, 2 vs second-to-last, etc.)
    for i in range(bracket_size // 2):
        team1 = teams_with_byes[i]
        team2 = teams_with_byes[bracket_size - 1 - i]
        
        match = {
            "team1": team1["name"],
            "team2": team2["name"],
            "winner": None
        }
        
        # Auto-advance if opponent is BYE
        if team2["name"] == "BYE":
            match["winner"] = team1["name"]
        elif team1["name"] == "BYE":
            match["winner"] = team2["name"]
        
        first_round_matches.append(match)
    
    rounds = [{
        "round": 1,
        "matches": first_round_matches
    }]
    
    # Generate subsequent rounds
    for round_num in range(2, num_rounds + 1):
        prev_round = rounds[-1]
        current_matches = []
        
        # Pair up winners from previous round
        for i in range(0, len(prev_round["matches"]), 2):
            if i + 1 < len(prev_round["matches"]):
                match1 = prev_round["matches"][i]
                match2 = prev_round["matches"][i + 1]
                
                team1 = match1.get("winner", f"Winner of Match {i + 1}")
                team2 = match2.get("winner", f"Winner of Match {i + 2}")
                
                current_matches.append({
                    "team1": team1,
                    "team2": team2,
                    "winner": None
                })
            else:
                # Odd number of matches, last team gets bye
                match1 = prev_round["matches"][i]
                team1 = match1.get("winner", f"Winner of Match {i + 1}")
                
                current_matches.append({
                    "team1": team1,
                    "team2": "BYE",
                    "winner": team1
                })
        
        rounds.append({
            "round": round_num,
            "matches": current_matches
        })
    
    return {"rounds": rounds}

def get_round_name(round_num: int, total_rounds: int) -> str:
    """Get descriptive name for tournament round"""
    if total_rounds == 1:
        return "Final"
    elif round_num == total_rounds:
        return "Grand Final"
    elif round_num == total_rounds - 1:
        return "Semi-Final"
    elif round_num == total_rounds - 2:
        return "Quarter-Final"
    elif round_num == 1:
        return "First Round"
    else:
        return f"Round {round_num}"
