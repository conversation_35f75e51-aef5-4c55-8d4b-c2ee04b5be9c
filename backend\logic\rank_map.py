"""
Valorant rank scoring system for tournament seeding
"""

RANK_SCORE = {
    "Iron": 1,
    "Bronze": 2,
    "Silver": 3,
    "Gold": 4,
    "Platinum": 5,
    "Diamond": 6,
    "Ascendant": 7,
    "Immortal": 8,
    "Radiant": 9
}

def get_rank_score(rank_name):
    """Get numeric score for a rank name"""
    if not rank_name:
        return 0
    
    # Handle rank names with numbers (e.g., "Gold_1_Rank.png")
    for rank, score in RANK_SCORE.items():
        if rank.lower() in rank_name.lower():
            return score
    
    return 0

def calculate_team_seed_score(players):
    """Calculate average rank score for a team"""
    if not players:
        return 0
    
    total_score = 0
    valid_ranks = 0
    
    for player in players:
        rank = player.get('rank', '')
        score = get_rank_score(rank)
        if score > 0:
            total_score += score
            valid_ranks += 1
    
    if valid_ranks == 0:
        return 0
    
    return round(total_score / valid_ranks, 2)
