"""
FastAPI backend for Valorant Tournament Bracket Generator
"""
import json
import os
from typing import List, Dict, Any, Optional
from fastapi import Fast<PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from pydantic import BaseModel

from logic.bracket_gen import generate_single_elimination_bracket, get_round_name
from logic.rank_map import calculate_team_seed_score

app = FastAPI(title="Valorant Tournament Bracket Generator")

# Enable CORS for frontend
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Serve static files (frontend)
app.mount("/static", StaticFiles(directory="../frontend"), name="static")

# Data file paths
TEAMS_FILE = "data/teams.json"
BRACKETS_FILE = "data/brackets.json"

# Pydantic models
class Player(BaseModel):
    name: str
    rank: Optional[str] = None

class Team(BaseModel):
    name: str
    players: List[Player]
    icon: Optional[str] = None

class BracketRequest(BaseModel):
    teams: List[str]  # Team names to include in bracket

# Utility functions
def load_teams() -> List[Dict[str, Any]]:
    """Load teams from JSON file"""
    if not os.path.exists(TEAMS_FILE):
        return []
    
    try:
        with open(TEAMS_FILE, 'r', encoding='utf-8') as f:
            return json.load(f)
    except (json.JSONDecodeError, FileNotFoundError):
        return []

def save_teams(teams: List[Dict[str, Any]]) -> None:
    """Save teams to JSON file"""
    os.makedirs(os.path.dirname(TEAMS_FILE), exist_ok=True)
    with open(TEAMS_FILE, 'w', encoding='utf-8') as f:
        json.dump(teams, f, indent=2, ensure_ascii=False)

def load_bracket() -> Dict[str, Any]:
    """Load bracket from JSON file"""
    if not os.path.exists(BRACKETS_FILE):
        return {}
    
    try:
        with open(BRACKETS_FILE, 'r', encoding='utf-8') as f:
            return json.load(f)
    except (json.JSONDecodeError, FileNotFoundError):
        return {}

def save_bracket(bracket: Dict[str, Any]) -> None:
    """Save bracket to JSON file"""
    os.makedirs(os.path.dirname(BRACKETS_FILE), exist_ok=True)
    with open(BRACKETS_FILE, 'w', encoding='utf-8') as f:
        json.dump(bracket, f, indent=2, ensure_ascii=False)

# API Endpoints
@app.get("/")
async def root():
    """Root endpoint"""
    return {"message": "Valorant Tournament Bracket Generator API"}

@app.get("/teams")
async def get_teams():
    """Get all teams"""
    teams = load_teams()
    return {"teams": teams}

@app.post("/teams/add")
async def add_team(team: Team):
    """Add a new team"""
    teams = load_teams()
    
    # Check if team name already exists
    if any(t["name"].lower() == team.name.lower() for t in teams):
        raise HTTPException(status_code=400, detail="Team name already exists")
    
    # Calculate seed score
    players_data = [{"name": p.name, "rank": p.rank} for p in team.players]
    seed_score = calculate_team_seed_score(players_data)
    
    new_team = {
        "name": team.name,
        "players": players_data,
        "icon": team.icon,
        "seedScore": seed_score
    }
    
    teams.append(new_team)
    save_teams(teams)
    
    return {"message": "Team added successfully", "team": new_team}

@app.delete("/teams/{team_name}")
async def delete_team(team_name: str):
    """Delete a team"""
    teams = load_teams()
    
    # Find and remove team
    teams = [t for t in teams if t["name"] != team_name]
    save_teams(teams)
    
    return {"message": f"Team {team_name} deleted successfully"}

@app.post("/bracket/generate")
async def generate_bracket():
    """Generate tournament bracket from all teams"""
    teams = load_teams()
    
    if len(teams) < 2:
        raise HTTPException(status_code=400, detail="At least 2 teams required for bracket generation")
    
    # Generate bracket
    bracket = generate_single_elimination_bracket(teams)
    
    # Add round names
    total_rounds = len(bracket["rounds"])
    for i, round_data in enumerate(bracket["rounds"]):
        round_data["roundName"] = get_round_name(i + 1, total_rounds)
    
    # Save bracket
    save_bracket(bracket)
    
    return {"message": "Bracket generated successfully", "bracket": bracket}

@app.get("/bracket")
async def get_bracket():
    """Get current bracket"""
    bracket = load_bracket()
    return {"bracket": bracket}

@app.delete("/bracket")
async def clear_bracket():
    """Clear current bracket"""
    save_bracket({})
    return {"message": "Bracket cleared successfully"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8001)
