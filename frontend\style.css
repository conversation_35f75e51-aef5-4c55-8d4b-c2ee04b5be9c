/* ===== VALORANT TOURNAMENT MATCHMAKER STYLES ===== */

/* Reset & Base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Raj<PERSON><PERSON>', 'Orbitron', sans-serif;
    background: linear-gradient(135deg, #0F1923 0%, #1A1A1A 100%);
    color: #FFFFFF;
    line-height: 1.6;
    min-height: 100vh;
    overflow-x: hidden;
}

/* CSS Variables */
:root {
    --valorant-red: #FF4655;
    --valorant-cyan: #00FFFF;
    --valorant-purple: #7B68EE;
    --dark-bg: #0F1923;
    --card-bg: #1E2328;
    --border-color: #3C3C41;
    --text-primary: #FFFFFF;
    --text-secondary: #AAABAD;
    --success-green: #00F5A0;
    --warning-yellow: #FFCC02;
}

/* Background Pattern */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%23FF4655" stroke-width="0.3" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
    z-index: -1;
}

/* Header */
.header {
    text-align: center;
    padding: 2rem 0;
    background: rgba(15, 25, 35, 0.9);
    backdrop-filter: blur(10px);
    border-bottom: 2px solid var(--valorant-red);
    margin-bottom: 3rem;
}

.header h1 {
    font-size: 3.5rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 3px;
    background: linear-gradient(45deg, var(--valorant-red), var(--valorant-cyan));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 0.5rem;
}

.header .subtitle {
    font-size: 1.2rem;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 2px;
    font-weight: 500;
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

/* Navigation */
.nav-tabs {
    display: flex;
    justify-content: center;
    gap: 0;
    margin-bottom: 3rem;
    background: var(--card-bg);
    border-radius: 12px;
    padding: 0.5rem;
    border: 1px solid var(--border-color);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.nav-tab {
    background: transparent;
    border: none;
    color: var(--text-secondary);
    padding: 1rem 2rem;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: all 0.3s ease;
    border-radius: 8px;
    font-family: 'Rajdhani', sans-serif;
    flex: 1;
    position: relative;
}

.nav-tab:hover {
    color: var(--valorant-red);
    background: rgba(255, 70, 85, 0.1);
    transform: translateY(-2px);
}

.nav-tab.active {
    background: linear-gradient(45deg, var(--valorant-red), #e63946);
    color: white;
    box-shadow: 0 5px 15px rgba(255, 70, 85, 0.3);
}

.nav-tab.active::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, var(--valorant-cyan), var(--valorant-red));
    border-radius: 10px;
    z-index: -1;
}

/* Tab Content */
.tab-content {
    display: none;
    animation: fadeIn 0.3s ease-in-out;
}

.tab-content.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Cards */
.card {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 2rem;
    margin-bottom: 2rem;
    position: relative;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(90deg, var(--valorant-red), var(--valorant-cyan));
}

.card:hover {
    transform: translateY(-5px);
    border-color: var(--valorant-red);
    box-shadow: 0 15px 40px rgba(255, 70, 85, 0.2);
}

.card h2 {
    font-size: 1.8rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    color: var(--text-primary);
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Form Styles */
.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--text-secondary);
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 1rem;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    font-size: 1rem;
    background: var(--dark-bg);
    color: var(--text-primary);
    font-family: 'Rajdhani', sans-serif;
    transition: all 0.3s ease;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--valorant-red);
    box-shadow: 0 0 10px rgba(255, 70, 85, 0.3);
}

/* Player Rows */
.player-row {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
    align-items: center;
    padding: 1rem;
    background: rgba(15, 25, 35, 0.5);
    border-radius: 8px;
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.player-row:hover {
    border-color: var(--valorant-cyan);
    background: rgba(0, 255, 255, 0.05);
}

.player-row input,
.player-row select {
    margin-bottom: 0;
    padding: 0.75rem;
}

.player-name {
    flex: 2;
}

.player-rank {
    flex: 1;
}

.rank-icon {
    width: 24px;
    height: 24px;
    margin-right: 0.5rem;
    vertical-align: middle;
    border-radius: 4px;
}

/* Buttons */
.btn {
    background: transparent;
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.9rem;
    letter-spacing: 1px;
    font-family: 'Rajdhani', sans-serif;
    position: relative;
    overflow: hidden;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.btn-primary {
    background: linear-gradient(45deg, var(--valorant-red), #e63946);
    border-color: var(--valorant-red);
    color: white;
}

.btn-primary:hover {
    background: linear-gradient(45deg, #e63946, #d62839);
    box-shadow: 0 5px 15px rgba(255, 70, 85, 0.4);
}

.btn-secondary {
    background: linear-gradient(45deg, var(--valorant-cyan), #00b8d4);
    border-color: var(--valorant-cyan);
    color: var(--dark-bg);
}

.btn-secondary:hover {
    background: linear-gradient(45deg, #00b8d4, #0097a7);
    box-shadow: 0 5px 15px rgba(0, 255, 255, 0.4);
}

.btn-danger {
    background: transparent;
    border-color: var(--valorant-red);
    color: var(--valorant-red);
}

.btn-danger:hover {
    background: var(--valorant-red);
    color: white;
}

/* Teams List */
.teams-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 1.5rem;
    margin-top: 2rem;
}

.team-card {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 1.5rem;
    transition: all 0.3s ease;
    position: relative;
}

.team-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, var(--valorant-cyan), var(--valorant-purple));
}

.team-card:hover {
    transform: translateY(-3px);
    border-color: var(--valorant-cyan);
    box-shadow: 0 10px 25px rgba(0, 255, 255, 0.2);
}

.team-card h3 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-primary);
    text-transform: uppercase;
}

.team-players {
    margin-bottom: 1rem;
}

.team-player {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
    color: var(--text-secondary);
}

.team-actions {
    display: flex;
    gap: 0.5rem;
    justify-content: flex-end;
}

.team-actions .btn {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 0 1rem;
    }
    
    .header h1 {
        font-size: 2.5rem;
    }
    
    .nav-tabs {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .nav-tab {
        padding: 0.75rem 1rem;
        font-size: 0.9rem;
    }
    
    .player-row {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .teams-list {
        grid-template-columns: 1fr;
    }
    
    .card {
        padding: 1.5rem;
    }
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid var(--border-color);
    border-radius: 50%;
    border-top-color: var(--valorant-red);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}
